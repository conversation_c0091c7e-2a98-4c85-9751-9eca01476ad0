import express, { Request, Response, NextFunction } from "express";
import { Server } from "http";
import { HealthcheckService } from "./healthcheck";
import bot from "../bot";
import { log } from "../utils/logger";

export class ExpressHttpServer {
  private app: express.Application;
  private server: Server | null = null;
  private readonly port: number;
  private isReady = false;

  constructor(port: number = 8080) {
    this.port = port;
    this.app = express();
    this.setupMiddleware();
    this.setupRoutes();
    this.setupErrorHandling();
  }

  setReady(ready: boolean): void {
    this.isReady = ready;
    log.info(`Server readiness: ${ready ? "READY" : "NOT READY"}`, {
      operation: "http_server",
      ready,
    });
  }

  private setupMiddleware(): void {
    // Parse JSON bodies
    this.app.use(express.json({ limit: "10mb" }));

    // Parse URL-encoded bodies
    this.app.use(express.urlencoded({ extended: true }));

    // CORS headers
    this.app.use((_req: Request, res: Response, next: NextFunction) => {
      res.header("Access-Control-Allow-Origin", "*");
      res.header("Access-Control-Allow-Methods", "GET, POST, OPTIONS");
      res.header("Access-Control-Allow-Headers", "Content-Type");
      next();
    });

    // Request logging
    this.app.use((req: Request, _res: Response, next: NextFunction) => {
      log.info(`${req.method} ${req.url}`, {
        operation: "http_request",
        method: req.method,
        url: req.url,
      });
      next();
    });
  }

  private setupRoutes(): void {
    // Health check endpoint
    this.app.get("/healthcheck", async (_req: Request, res: Response) => {
      try {
        const lastHealthcheck = await HealthcheckService.getLastHealthcheck();
        const isHealthy = await HealthcheckService.isHealthy();

        const response = {
          status: isHealthy ? "healthy" : "unhealthy",
          lastHealthcheck,
          timestamp: new Date().toISOString(),
          service: "marketplace-bot",
        };

        res.status(isHealthy ? 200 : 503).json(response);
      } catch (error) {
        log.error("Error in healthcheck endpoint", error, {
          operation: "healthcheck",
        });

        const errorResponse = {
          status: "error",
          message: "Failed to check health status",
          timestamp: new Date().toISOString(),
          service: "marketplace-bot",
        };

        res.status(500).json(errorResponse);
      }
    });

    // Readiness endpoint
    this.app.get("/readiness", (_req: Request, res: Response) => {
      const response = {
        status: this.isReady ? "ready" : "not ready",
        timestamp: new Date().toISOString(),
        service: "marketplace-bot",
      };

      res.status(this.isReady ? 200 : 503).json(response);
    });

    // Webhook endpoint
    this.app.post("/webhook", async (req: Request, res: Response) => {
      try {
        const update = req.body;

        log.webhookLog("Received webhook update", {
          operation: "webhook_processing",
          updateId: update.update_id,
        });

        // Process the update with Telegraf
        await bot.handleUpdate(update);

        res.json({ ok: true });
      } catch (error) {
        log.error("Error processing webhook update", error, {
          operation: "webhook_processing",
        });
        res.status(500).json({ ok: false, error: "Failed to process update" });
      }
    });

    // Root endpoint
    this.app.get("/", (_req: Request, res: Response) => {
      const response = {
        service: "marketplace-bot",
        status: "running",
        ready: this.isReady,
        timestamp: new Date().toISOString(),
        endpoints: ["/healthcheck", "/readiness", "/webhook"],
      };

      res.json(response);
    });

    // 404 handler for unknown routes
    this.app.use("*", (req: Request, res: Response) => {
      const notFoundResponse = {
        error: "Not Found",
        message: `Route ${req.url} not found`,
        timestamp: new Date().toISOString(),
      };

      res.status(404).json(notFoundResponse);
    });
  }

  private setupErrorHandling(): void {
    // Global error handler
    this.app.use(
      (error: Error, req: Request, res: Response, _next: NextFunction) => {
        log.error("Request handling error", error, {
          operation: "http_request",
          url: req.url,
          method: req.method,
        });

        if (!res.headersSent) {
          res.status(500).json({ error: "Internal Server Error" });
        }
      }
    );
  }

  start(): Promise<void> {
    return new Promise((resolve, reject) => {
      try {
        this.server = this.app.listen(this.port, () => {
          log.info(`Express HTTP server running on port ${this.port}`, {
            operation: "http_server_start",
            port: this.port,
            healthcheckUrl: `http://localhost:${this.port}/healthcheck`,
          });
          resolve();
        });

        this.server.on("error", (error: Error) => {
          log.error("HTTP server error", error, {
            operation: "http_server_start",
            port: this.port,
          });
          reject(error);
        });
      } catch (error) {
        reject(error);
      }
    });
  }

  stop(): Promise<void> {
    return new Promise((resolve) => {
      if (this.server) {
        log.info("Stopping Express HTTP server", {
          operation: "http_server_stop",
        });

        this.server.close(() => {
          log.info("Express HTTP server stopped", {
            operation: "http_server_stop",
            status: "completed",
          });
          resolve();
        });
      } else {
        resolve();
      }
    });
  }
}

const PORT = parseInt(process.env.PORT ?? "8080");
export const expressHttpServer = new ExpressHttpServer(PORT);
